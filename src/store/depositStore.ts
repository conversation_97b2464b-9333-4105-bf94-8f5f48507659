import { makeAutoObservable } from "mobx";
import { Network } from "@/types/network.ts";
import { networks } from "@/config/network.ts";

export class DepositStore {
  fromNetwork: Network;
  destNetwork: Network;


  constructor() {
    this.fromNetwork = networks[0] || null;
    this.destNetwork = networks[1] || null;

    makeAutoObservable(this);
  }

  setFromNetwork(network: Network) {
    this.fromNetwork = network;
    this.destNetwork = network.destNetworks[0];
  }

  setDestNetwork(network: Network) {
    this.destNetwork = network;
  }

  swapNetworks() {
    const temp = this.fromNetwork;
    this.fromNetwork = this.destNetwork;
    this.destNetwork = temp;
  }



}
